<div class="chat-container">
  <div class="chat-header">
    <h2>Real-time Chat App</h2>
    <div class="connection-status">
      <span [class]="isConnected ? 'connected' : 'disconnected'">
        {{ isConnected ? 'Connected' : 'Disconnected' }}
      </span>
      <button *ngIf="isConnected" (click)="disconnect()" class="disconnect-btn">
        Disconnect
      </button>
    </div>
  </div>

  <!-- Username Input -->
  <div *ngIf="showUsernameInput" class="username-section">
    <div class="username-input">
      <label for="username">Enter your username:</label>
      <input
        id="username"
        type="text"
        [(ngModel)]="userName"
        placeholder="Your username"
        (keypress)="$event.key === 'Enter' && connectToChat()"
        maxlength="20"
      />
      <button (click)="connectToChat()" [disabled]="!userName.trim()">
        Connect to Chat
      </button>
    </div>
  </div>

  <!-- Chat Interface -->
  <div *ngIf="!showUsernameInput" class="chat-interface">
    <div class="user-info">
      <span>Welcome, <strong>{{ userName }}</strong>!</span>
    </div>

    <!-- Messages Display -->
    <div class="messages-container">
      <div
        *ngFor="let message of messages$ | async"
        class="message"
        [class.system-message]="message.user === 'System'"
        [class.own-message]="message.user === userName"
      >
        <div class="message-header">
          <span class="username">{{ message.user }}</span>
          <span class="timestamp">{{ message.timestamp }}</span>
        </div>
        <div class="message-content">{{ message.message }}</div>
      </div>
    </div>

    <!-- Message Input -->
    <div class="message-input-section">
      <div class="message-input">
        <input
          type="text"
          [(ngModel)]="currentMessage"
          placeholder="Type your message..."
          (keypress)="onKeyPress($event)"
          maxlength="500"
        />
        <button
          (click)="sendMessage()"
          [disabled]="!currentMessage.trim() || !isConnected"
        >
          Send
        </button>
      </div>
    </div>
  </div>
</div>
