/**
 * @license Angular v20.0.3
 * (c) 2010-2025 Google LLC. https://angular.io/
 * License: MIT
 */

import { Location, LocationStrategy, CommonModule } from '../common_module.d-Cpp8wYHt.js';
import { PlatformLocation } from '../platform_location.d-Lbv6Ueec.js';
import { UpgradeModule } from '@angular/upgrade/static';
import * as i0 from '@angular/core';
import { InjectionToken, ModuleWithProviders } from '@angular/core';
import 'rxjs';

/**
 * A codec for encoding and decoding URL parts.
 *
 * @publicApi
 **/
declare abstract class UrlCodec {
    /**
     * Encodes the path from the provided string
     *
     * @param path The path string
     */
    abstract encodePath(path: string): string;
    /**
     * Decodes the path from the provided string
     *
     * @param path The path string
     */
    abstract decodePath(path: string): string;
    /**
     * Encodes the search string from the provided string or object
     *
     * @param path The path string or object
     */
    abstract encodeSearch(search: string | {
        [k: string]: unknown;
    }): string;
    /**
     * Decodes the search objects from the provided string
     *
     * @param path The path string
     */
    abstract decodeSearch(search: string): {
        [k: string]: unknown;
    };
    /**
     * Encodes the hash from the provided string
     *
     * @param path The hash string
     */
    abstract encodeHash(hash: string): string;
    /**
     * Decodes the hash from the provided string
     *
     * @param path The hash string
     */
    abstract decodeHash(hash: string): string;
    /**
     * Normalizes the URL from the provided string
     *
     * @param path The URL string
     */
    abstract normalize(href: string): string;
    /**
     * Normalizes the URL from the provided string, search, hash, and base URL parameters
     *
     * @param path The URL path
     * @param search The search object
     * @param hash The has string
     * @param baseUrl The base URL for the URL
     */
    abstract normalize(path: string, search: {
        [k: string]: unknown;
    }, hash: string, baseUrl?: string): string;
    /**
     * Checks whether the two strings are equal
     * @param valA First string for comparison
     * @param valB Second string for comparison
     */
    abstract areEqual(valA: string, valB: string): boolean;
    /**
     * Parses the URL string based on the base URL
     *
     * @param url The full URL string
     * @param base The base for the URL
     */
    abstract parse(url: string, base?: string): {
        href: string;
        protocol: string;
        host: string;
        search: string;
        hash: string;
        hostname: string;
        port: string;
        pathname: string;
    };
}
/**
 * A `UrlCodec` that uses logic from AngularJS to serialize and parse URLs
 * and URL parameters.
 *
 * @publicApi
 */
declare class AngularJSUrlCodec implements UrlCodec {
    encodePath(path: string): string;
    encodeSearch(search: string | {
        [k: string]: unknown;
    }): string;
    encodeHash(hash: string): string;
    decodePath(path: string, html5Mode?: boolean): string;
    decodeSearch(search: string): {
        [k: string]: unknown;
    };
    decodeHash(hash: string): string;
    normalize(href: string): string;
    normalize(path: string, search: {
        [k: string]: unknown;
    }, hash: string, baseUrl?: string): string;
    areEqual(valA: string, valB: string): boolean;
    parse(url: string, base?: string): {
        href: string;
        protocol: string;
        host: string;
        search: string;
        hash: string;
        hostname: string;
        port: string;
        pathname: string;
    };
}

/**
 * Location service that provides a drop-in replacement for the $location service
 * provided in AngularJS.
 *
 * @see [Using the Angular Unified Location Service](guide/upgrade#using-the-unified-angular-location-service)
 *
 * @publicApi
 */
declare class $locationShim {
    private location;
    private platformLocation;
    private urlCodec;
    private locationStrategy;
    private initializing;
    private updateBrowser;
    private $$absUrl;
    private $$url;
    private $$protocol;
    private $$host;
    private $$port;
    private $$replace;
    private $$path;
    private $$search;
    private $$hash;
    private $$state;
    private $$changeListeners;
    private cachedState;
    private urlChanges;
    private readonly removeOnUrlChangeFn;
    constructor($injector: any, location: Location, platformLocation: PlatformLocation, urlCodec: UrlCodec, locationStrategy: LocationStrategy);
    private initialize;
    private resetBrowserUpdate;
    private lastHistoryState;
    private lastBrowserUrl;
    private browserUrl;
    private lastCachedState;
    private cacheState;
    /**
     * This function emulates the $browser.state() function from AngularJS. It will cause
     * history.state to be cached unless changed with deep equality check.
     */
    private browserState;
    private stripBaseUrl;
    private getServerBase;
    private parseAppUrl;
    /**
     * Registers listeners for URL changes. This API is used to catch updates performed by the
     * AngularJS framework. These changes are a subset of the `$locationChangeStart` and
     * `$locationChangeSuccess` events which fire when AngularJS updates its internally-referenced
     * version of the browser URL.
     *
     * It's possible for `$locationChange` events to happen, but for the browser URL
     * (window.location) to remain unchanged. This `onChange` callback will fire only when AngularJS
     * actually updates the browser URL (window.location).
     *
     * @param fn The callback function that is triggered for the listener when the URL changes.
     * @param err The callback function that is triggered when an error occurs.
     */
    onChange(fn: (url: string, state: unknown, oldUrl: string, oldState: unknown) => void, err?: (e: Error) => void): void;
    /**
     * Parses the provided URL, and sets the current URL to the parsed result.
     *
     * @param url The URL string.
     */
    $$parse(url: string): void;
    /**
     * Parses the provided URL and its relative URL.
     *
     * @param url The full URL string.
     * @param relHref A URL string relative to the full URL string.
     */
    $$parseLinkUrl(url: string, relHref?: string | null): boolean;
    private setBrowserUrlWithFallback;
    private composeUrls;
    /**
     * Retrieves the full URL representation with all segments encoded according to
     * rules specified in
     * [RFC 3986](https://tools.ietf.org/html/rfc3986).
     *
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo
     * let absUrl = $location.absUrl();
     * // => "http://example.com/#/some/path?foo=bar&baz=xoxo"
     * ```
     */
    absUrl(): string;
    /**
     * Retrieves the current URL, or sets a new URL. When setting a URL,
     * changes the path, search, and hash, and returns a reference to its own instance.
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo
     * let url = $location.url();
     * // => "/some/path?foo=bar&baz=xoxo"
     * ```
     */
    url(): string;
    url(url: string): this;
    /**
     * Retrieves the protocol of the current URL.
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo
     * let protocol = $location.protocol();
     * // => "http"
     * ```
     */
    protocol(): string;
    /**
     * Retrieves the protocol of the current URL.
     *
     * In contrast to the non-AngularJS version `location.host` which returns `hostname:port`, this
     * returns the `hostname` portion only.
     *
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo
     * let host = $location.host();
     * // => "example.com"
     *
     * // given URL http://user:<EMAIL>:8080/#/some/path?foo=bar&baz=xoxo
     * host = $location.host();
     * // => "example.com"
     * host = location.host;
     * // => "example.com:8080"
     * ```
     */
    host(): string;
    /**
     * Retrieves the port of the current URL.
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo
     * let port = $location.port();
     * // => 80
     * ```
     */
    port(): number | null;
    /**
     * Retrieves the path of the current URL, or changes the path and returns a reference to its own
     * instance.
     *
     * Paths should always begin with forward slash (/). This method adds the forward slash
     * if it is missing.
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo
     * let path = $location.path();
     * // => "/some/path"
     * ```
     */
    path(): string;
    path(path: string | number | null): this;
    /**
     * Retrieves a map of the search parameters of the current URL, or changes a search
     * part and returns a reference to its own instance.
     *
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo
     * let searchObject = $location.search();
     * // => {foo: 'bar', baz: 'xoxo'}
     *
     * // set foo to 'yipee'
     * $location.search('foo', 'yipee');
     * // $location.search() => {foo: 'yipee', baz: 'xoxo'}
     * ```
     *
     * @param {string|Object.<string>|Object.<Array.<string>>} search New search params - string or
     * hash object.
     *
     * When called with a single argument the method acts as a setter, setting the `search` component
     * of `$location` to the specified value.
     *
     * If the argument is a hash object containing an array of values, these values will be encoded
     * as duplicate search parameters in the URL.
     *
     * @param {(string|Number|Array<string>|boolean)=} paramValue If `search` is a string or number,
     *     then `paramValue`
     * will override only a single search property.
     *
     * If `paramValue` is an array, it will override the property of the `search` component of
     * `$location` specified via the first argument.
     *
     * If `paramValue` is `null`, the property specified via the first argument will be deleted.
     *
     * If `paramValue` is `true`, the property specified via the first argument will be added with no
     * value nor trailing equal sign.
     *
     * @return {Object} The parsed `search` object of the current URL, or the changed `search` object.
     */
    search(): {
        [key: string]: unknown;
    };
    search(search: string | number | {
        [key: string]: unknown;
    }): this;
    search(search: string | number | {
        [key: string]: unknown;
    }, paramValue: null | undefined | string | number | boolean | string[]): this;
    /**
     * Retrieves the current hash fragment, or changes the hash fragment and returns a reference to
     * its own instance.
     *
     * ```js
     * // given URL http://example.com/#/some/path?foo=bar&baz=xoxo#hashValue
     * let hash = $location.hash();
     * // => "hashValue"
     * ```
     */
    hash(): string;
    hash(hash: string | number | null): this;
    /**
     * Changes to `$location` during the current `$digest` will replace the current
     * history record, instead of adding a new one.
     */
    replace(): this;
    /**
     * Retrieves the history state object when called without any parameter.
     *
     * Change the history state object when called with one parameter and return `$location`.
     * The state object is later passed to `pushState` or `replaceState`.
     *
     * This method is supported only in HTML5 mode and only in browsers supporting
     * the HTML5 History API methods such as `pushState` and `replaceState`. If you need to support
     * older browsers (like Android < 4.0), don't use this method.
     *
     */
    state(): unknown;
    state(state: unknown): this;
}
/**
 * The factory function used to create an instance of the `$locationShim` in Angular,
 * and provides an API-compatible `$locationProvider` for AngularJS.
 *
 * @publicApi
 */
declare class $locationShimProvider {
    private ngUpgrade;
    private location;
    private platformLocation;
    private urlCodec;
    private locationStrategy;
    constructor(ngUpgrade: UpgradeModule, location: Location, platformLocation: PlatformLocation, urlCodec: UrlCodec, locationStrategy: LocationStrategy);
    /**
     * Factory method that returns an instance of the $locationShim
     */
    $get(): $locationShim;
    /**
     * Stub method used to keep API compatible with AngularJS. This setting is configured through
     * the LocationUpgradeModule's `config` method in your Angular app.
     */
    hashPrefix(prefix?: string): void;
    /**
     * Stub method used to keep API compatible with AngularJS. This setting is configured through
     * the LocationUpgradeModule's `config` method in your Angular app.
     */
    html5Mode(mode?: any): void;
}

/**
 * Configuration options for LocationUpgrade.
 *
 * @publicApi
 */
interface LocationUpgradeConfig {
    /**
     * Configures whether the location upgrade module should use the `HashLocationStrategy`
     * or the `PathLocationStrategy`
     */
    useHash?: boolean;
    /**
     * Configures the hash prefix used in the URL when using the `HashLocationStrategy`
     */
    hashPrefix?: string;
    /**
     * Configures the URL codec for encoding and decoding URLs. Default is the `AngularJSCodec`
     */
    urlCodec?: typeof UrlCodec;
    /**
     * Configures the base href when used in server-side rendered applications
     */
    serverBaseHref?: string;
    /**
     * Configures the base href when used in client-side rendered applications
     */
    appBaseHref?: string;
}
/**
 * A provider token used to configure the location upgrade module.
 *
 * @publicApi
 */
declare const LOCATION_UPGRADE_CONFIGURATION: InjectionToken<LocationUpgradeConfig>;
/**
 * `NgModule` used for providing and configuring Angular's Unified Location Service for upgrading.
 *
 * @see [Using the Unified Angular Location Service](https://angular.io/guide/upgrade#using-the-unified-angular-location-service)
 *
 * @publicApi
 */
declare class LocationUpgradeModule {
    static config(config?: LocationUpgradeConfig): ModuleWithProviders<LocationUpgradeModule>;
    static ɵfac: i0.ɵɵFactoryDeclaration<LocationUpgradeModule, never>;
    static ɵmod: i0.ɵɵNgModuleDeclaration<LocationUpgradeModule, never, [typeof CommonModule], never>;
    static ɵinj: i0.ɵɵInjectorDeclaration<LocationUpgradeModule>;
}

export { $locationShim, $locationShimProvider, AngularJSUrlCodec, LOCATION_UPGRADE_CONFIGURATION, LocationUpgradeModule, UrlCodec };
export type { LocationUpgradeConfig };
