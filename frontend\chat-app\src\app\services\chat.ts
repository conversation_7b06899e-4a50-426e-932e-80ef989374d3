import { Injectable } from '@angular/core';
import { HubConnection, HubConnectionBuilder } from '@microsoft/signalr';
import { BehaviorSubject, Observable } from 'rxjs';

export interface ChatMessage {
  user: string;
  message: string;
  timestamp: string;
}

@Injectable({
  providedIn: 'root',
})
export class ChatService {
  private hubConnection: HubConnection | null = null;
  private messagesSubject = new BehaviorSubject<ChatMessage[]>([]);
  private connectedUsersSubject = new BehaviorSubject<string[]>([]);

  public messages$ = this.messagesSubject.asObservable();
  public connectedUsers$ = this.connectedUsersSubject.asObservable();
  public isConnected = false;

  constructor() {}

  public async startConnection(): Promise<void> {
    this.hubConnection = new HubConnectionBuilder()
      .withUrl('http://localhost:5000/chathub')
      .build();

    try {
      await this.hubConnection.start();
      this.isConnected = true;
      console.log('SignalR connection started');
      this.registerEventHandlers();
    } catch (error) {
      console.error('Error starting SignalR connection:', error);
      this.isConnected = false;
    }
  }

  public async stopConnection(): Promise<void> {
    if (this.hubConnection) {
      await this.hubConnection.stop();
      this.isConnected = false;
      console.log('SignalR connection stopped');
    }
  }

  public async sendMessage(user: string, message: string): Promise<void> {
    if (this.hubConnection && this.isConnected) {
      try {
        await this.hubConnection.invoke('SendMessage', user, message);
      } catch (error) {
        console.error('Error sending message:', error);
      }
    }
  }

  public async joinRoom(roomName: string, userName: string): Promise<void> {
    if (this.hubConnection && this.isConnected) {
      try {
        await this.hubConnection.invoke('JoinRoom', roomName, userName);
      } catch (error) {
        console.error('Error joining room:', error);
      }
    }
  }

  private registerEventHandlers(): void {
    if (!this.hubConnection) return;

    this.hubConnection.on(
      'ReceiveMessage',
      (user: string, message: string, timestamp: string) => {
        const newMessage: ChatMessage = { user, message, timestamp };
        const currentMessages = this.messagesSubject.value;
        this.messagesSubject.next([...currentMessages, newMessage]);
      },
    );

    this.hubConnection.on('UserJoined', (userName: string, message: string) => {
      const joinMessage: ChatMessage = {
        user: 'System',
        message: message,
        timestamp: new Date().toLocaleTimeString(),
      };
      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next([...currentMessages, joinMessage]);
    });

    this.hubConnection.on('UserLeft', (userName: string, message: string) => {
      const leaveMessage: ChatMessage = {
        user: 'System',
        message: message,
        timestamp: new Date().toLocaleTimeString(),
      };
      const currentMessages = this.messagesSubject.value;
      this.messagesSubject.next([...currentMessages, leaveMessage]);
    });
  }
}
