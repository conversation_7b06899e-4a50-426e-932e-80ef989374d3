.chat-container {
  max-width: 800px;
  margin: 20px auto;
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
}

.chat-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.chat-header h2 {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.connection-status {
  display: flex;
  align-items: center;
  gap: 10px;
}

.connected {
  color: #4ade80;
  font-weight: 600;
}

.disconnected {
  color: #f87171;
  font-weight: 600;
}

.disconnect-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s;
}

.disconnect-btn:hover {
  background: rgba(255, 255, 255, 0.3);
}

.username-section {
  padding: 40px;
  text-align: center;
  background: #f8fafc;
}

.username-input {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 15px;
  max-width: 300px;
  margin: 0 auto;
}

.username-input label {
  font-size: 1.1rem;
  font-weight: 500;
  color: #374151;
}

.username-input input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 8px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.username-input input:focus {
  outline: none;
  border-color: #667eea;
}

.username-input button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 24px;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s;
}

.username-input button:hover:not(:disabled) {
  transform: translateY(-2px);
}

.username-input button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

.chat-interface {
  display: flex;
  flex-direction: column;
  height: 600px;
}

.user-info {
  background: #f1f5f9;
  padding: 15px 20px;
  border-bottom: 1px solid #e2e8f0;
  font-size: 0.95rem;
  color: #475569;
}

.messages-container {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: #ffffff;
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.message {
  display: flex;
  flex-direction: column;
  max-width: 70%;
  animation: fadeIn 0.3s ease-in;
}

.message.own-message {
  align-self: flex-end;
}

.message.own-message .message-content {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
}

.message.system-message {
  align-self: center;
  max-width: 90%;
}

.message.system-message .message-content {
  background: #fef3c7;
  color: #92400e;
  text-align: center;
  font-style: italic;
}

.message-header {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 0.85rem;
  color: #6b7280;
}

.username {
  font-weight: 600;
}

.timestamp {
  font-size: 0.8rem;
}

.message-content {
  background: #f3f4f6;
  padding: 12px 16px;
  border-radius: 18px;
  word-wrap: break-word;
  line-height: 1.4;
}

.message-input-section {
  background: #f8fafc;
  padding: 20px;
  border-top: 1px solid #e2e8f0;
}

.message-input {
  display: flex;
  gap: 10px;
  align-items: center;
}

.message-input input {
  flex: 1;
  padding: 12px 16px;
  border: 2px solid #e5e7eb;
  border-radius: 25px;
  font-size: 1rem;
  transition: border-color 0.2s;
}

.message-input input:focus {
  outline: none;
  border-color: #667eea;
}

.message-input button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  padding: 12px 20px;
  border-radius: 25px;
  font-size: 1rem;
  font-weight: 500;
  cursor: pointer;
  transition: transform 0.2s;
  min-width: 80px;
}

.message-input button:hover:not(:disabled) {
  transform: translateY(-1px);
}

.message-input button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  transform: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Scrollbar styling */
.messages-container::-webkit-scrollbar {
  width: 6px;
}

.messages-container::-webkit-scrollbar-track {
  background: #f1f1f1;
}

.messages-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.messages-container::-webkit-scrollbar-thumb:hover {
  background: #a1a1a1;
}
