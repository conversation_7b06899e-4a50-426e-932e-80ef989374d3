import { Component, OnInit, <PERSON><PERSON><PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ChatService, ChatMessage } from '../../services/chat';
import { Observable } from 'rxjs';

@Component({
  selector: 'app-chat',
  imports: [CommonModule, FormsModule],
  templateUrl: './chat.html',
  styleUrl: './chat.css',
})
export class ChatComponent implements OnInit, OnDestroy {
  messages$: Observable<ChatMessage[]>;
  currentMessage = '';
  userName = '';
  isConnected = false;
  showUsernameInput = true;

  constructor(private chatService: ChatService) {
    this.messages$ = this.chatService.messages$;
  }

  async ngOnInit() {
    // Check if already connected
    this.isConnected = this.chatService.isConnected;
  }

  async ngOnDestroy() {
    if (this.isConnected) {
      await this.chatService.stopConnection();
    }
  }

  async connectToChat() {
    if (this.userName.trim()) {
      try {
        await this.chatService.startConnection();
        this.isConnected = true;
        this.showUsernameInput = false;

        // Join the general room
        await this.chatService.joinRoom('General', this.userName);
      } catch (error) {
        console.error('Failed to connect to chat:', error);
        alert('Failed to connect to chat. Please try again.');
      }
    }
  }

  async sendMessage() {
    if (this.currentMessage.trim() && this.isConnected) {
      await this.chatService.sendMessage(this.userName, this.currentMessage);
      this.currentMessage = '';
    }
  }

  async disconnect() {
    await this.chatService.stopConnection();
    this.isConnected = false;
    this.showUsernameInput = true;
    this.userName = '';
  }

  onKeyPress(event: KeyboardEvent) {
    if (event.key === 'Enter') {
      this.sendMessage();
    }
  }
}
